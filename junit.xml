<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="24" failures="14" errors="0" time="1253.447">
  <testsuite name="Load Testing" errors="0" failures="4" skipped="0" timestamp="2025-06-09T10:24:13" time="113.996" tests="9">
    <testcase classname="Load Testing API Endpoint Load Tests should handle 100 concurrent GraphQL requests" name="Load Testing API Endpoint Load Tests should handle 100 concurrent GraphQL requests" time="10.54">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 0
Received: 1700
    at toBe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:75:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at runNextTicks (node:internal/process/task_queues:64:3)
    at listOnTimeout (node:internal/timers:538:9)
    at processTimers (node:internal/timers:512:7)</failure>
    </testcase>
    <testcase classname="Load Testing API Endpoint Load Tests should handle GraphQL mutations under load" name="Load Testing API Endpoint Load Tests should handle GraphQL mutations under load" time="12.548">
    </testcase>
    <testcase classname="Load Testing API Endpoint Load Tests should handle payment methods query under load" name="Load Testing API Endpoint Load Tests should handle payment methods query under load" time="11.445">
    </testcase>
    <testcase classname="Load Testing GraphQL Load Tests should handle GraphQL queries under load" name="Load Testing GraphQL Load Tests should handle GraphQL queries under load" time="11.457">
      <failure>Error: expect(received).toBeLessThan(expected)

Expected: &lt; 300
Received:   683.63
    at toBeLessThan (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:206:35)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at runNextTicks (node:internal/process/task_queues:64:3)
    at processTimers (node:internal/timers:509:9)</failure>
    </testcase>
    <testcase classname="Load Testing GraphQL Load Tests should handle complex GraphQL queries under load" name="Load Testing GraphQL Load Tests should handle complex GraphQL queries under load" time="10.715">
    </testcase>
    <testcase classname="Load Testing Database Load Tests should handle concurrent database operations" name="Load Testing Database Load Tests should handle concurrent database operations" time="0.389">
      <failure>MongoServerError: E11000 duplicate key error collection: firespoon_test.customers index: phone_1 dup key: { phone: &quot;+1234567890&quot; }
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/operations/insert.ts:85:25
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection_pool.ts:569:13
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/sdam/server.ts:363:13
    at handleOperationResult (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/sdam/server.ts:496:14)
    at Connection.onMessage (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection.ts:461:5)
    at MessageStream.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection.ts:241:56)
    at MessageStream.emit (node:events:517:28)
    at processIncomingData (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/message_stream.ts:188:12)
    at MessageStream._write (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/message_stream.ts:69:5)
    at writeOrBuffer (node:internal/streams/writable:392:12)
    at _write (node:internal/streams/writable:333:10)
    at MessageStream.Writable.write (node:internal/streams/writable:337:10)
    at Socket.ondata (node:internal/streams/readable:809:22)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)</failure>
    </testcase>
    <testcase classname="Load Testing Memory and Resource Tests should not have memory leaks under sustained load" name="Load Testing Memory and Resource Tests should not have memory leaks under sustained load" time="31.279">
    </testcase>
    <testcase classname="Load Testing Error Rate Tests should maintain low error rate under high load" name="Load Testing Error Rate Tests should maintain low error rate under high load" time="17.066">
    </testcase>
    <testcase classname="Performance Benchmarks should meet performance SLA requirements" name="Performance Benchmarks should meet performance SLA requirements" time="0.001">
      <failure>ReferenceError: baseURL is not defined
    at baseURL (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:412:17)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Stress Testing" errors="0" failures="2" skipped="0" timestamp="2025-06-09T10:26:07" time="172.828" tests="7">
    <testcase classname="Stress Testing Extreme Load Tests should survive 500 concurrent connections" name="Stress Testing Extreme Load Tests should survive 500 concurrent connections" time="31.89">
    </testcase>
    <testcase classname="Stress Testing Extreme Load Tests should handle burst traffic patterns" name="Stress Testing Extreme Load Tests should handle burst traffic patterns" time="47.763">
    </testcase>
    <testcase classname="Stress Testing Resource Exhaustion Tests should handle memory pressure gracefully" name="Stress Testing Resource Exhaustion Tests should handle memory pressure gracefully" time="21.37">
    </testcase>
    <testcase classname="Stress Testing Resource Exhaustion Tests should handle connection pool exhaustion" name="Stress Testing Resource Exhaustion Tests should handle connection pool exhaustion" time="10.902">
    </testcase>
    <testcase classname="Stress Testing Recovery and Resilience Tests should recover from temporary overload" name="Stress Testing Recovery and Resilience Tests should recover from temporary overload" time="48.833">
      <failure>Error: expect(received).toBeLessThan(expected)

Expected: &lt; 0
Received:   0
    at toBeLessThan (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/stress.test.js:298:35)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at runNextTicks (node:internal/process/task_queues:64:3)
    at processTimers (node:internal/timers:509:9)</failure>
    </testcase>
    <testcase classname="Stress Testing Database Stress Tests should handle database connection stress" name="Stress Testing Database Stress Tests should handle database connection stress" time="1.383">
      <failure>Error: expect(received).toBeGreaterThan(expected)

Expected: &gt; 250
Received:   0
    at toBeGreaterThan (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/stress.test.js:367:26)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at runNextTicks (node:internal/process/task_queues:64:3)
    at processTimers (node:internal/timers:509:9)</failure>
    </testcase>
    <testcase classname="Stress Testing API Rate Limiting Tests should enforce rate limits under stress" name="Stress Testing API Rate Limiting Tests should enforce rate limits under stress" time="5.533">
    </testcase>
  </testsuite>
  <testsuite name="Concurrency Testing" errors="0" failures="8" skipped="0" timestamp="2025-06-09T10:29:01" time="964.571" tests="8">
    <testcase classname="Concurrency Testing Order Creation Concurrency should handle concurrent order creation without data corruption" name="Concurrency Testing Order Creation Concurrency should handle concurrent order creation without data corruption" time="120.004">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Order Creation Concurrency should handle concurrent order status updates correctly" name="Concurrency Testing Order Creation Concurrency should handle concurrent order status updates correctly" time="120.001">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Payment Processing Concurrency should prevent duplicate payment processing" name="Concurrency Testing Payment Processing Concurrency should prevent duplicate payment processing" time="120.002">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Database Transaction Concurrency should handle concurrent customer updates without race conditions" name="Concurrency Testing Database Transaction Concurrency should handle concurrent customer updates without race conditions" time="120.001">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Database Transaction Concurrency should handle concurrent order item updates correctly" name="Concurrency Testing Database Transaction Concurrency should handle concurrent order item updates correctly" time="120.002">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Session and Cache Concurrency should handle concurrent session operations" name="Concurrency Testing Session and Cache Concurrency should handle concurrent session operations" time="120.003">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Resource Locking Tests should handle inventory updates with proper locking" name="Concurrency Testing Resource Locking Tests should handle inventory updates with proper locking" time="120.004">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Concurrency Testing Error Handling Under Concurrency should gracefully handle errors in concurrent operations" name="Concurrency Testing Error Handling Under Concurrency should gracefully handle errors in concurrent operations" time="120.003">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/array/methods/index.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/DocumentArray/index.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$getAllSubdocs (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3596:37)
    at _getPathsToValidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2687:23)
    at model.Object.&lt;anonymous&gt;.Document.$__validate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:2895:23)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/kareem/index.js:497:25
    at processTicksAndRejections (node:internal/process/task_queues:77:11)</failure>
      <failure>Error: thrown: &quot;Exceeded timeout of 120000 ms for a hook.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at beforeEach (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:29:3)
    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)
    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/concurrency.test.js:14:1)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
</testsuites>