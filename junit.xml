<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="14" failures="2" errors="0" time="26.666">
  <testsuite name="Basic E2E Tests" errors="0" failures="2" skipped="0" timestamp="2025-06-09T00:32:05" time="24.027" tests="6">
    <testcase classname="Basic E2E Tests Application Health should respond to health check" name="Basic E2E Tests Application Health should respond to health check" time="0.272">
    </testcase>
    <testcase classname="Basic E2E Tests Application Health should have working GraphQL endpoint" name="Basic E2E Tests Application Health should have working GraphQL endpoint" time="0.053">
    </testcase>
    <testcase classname="Basic E2E Tests Application Health should have WhatsApp endpoints" name="Basic E2E Tests Application Health should have WhatsApp endpoints" time="0.028">
    </testcase>
    <testcase classname="Basic E2E Tests Database Operations should be able to create and query customer directly" name="Basic E2E Tests Database Operations should be able to create and query customer directly" time="10.121">
      <failure>MongooseError: Operation `customers.insertOne()` buffering timed out after 10000ms
    at Timeout.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:188:23)
    at listOnTimeout (node:internal/timers:569:17)
    at processTimers (node:internal/timers:512:7)</failure>
    </testcase>
    <testcase classname="Basic E2E Tests Database Operations should be able to create restaurant owner directly" name="Basic E2E Tests Database Operations should be able to create restaurant owner directly" time="10.114">
      <failure>MongooseError: Operation `owners.insertOne()` buffering timed out after 10000ms
    at Timeout.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:188:23)
    at listOnTimeout (node:internal/timers:569:17)
    at processTimers (node:internal/timers:512:7)</failure>
    </testcase>
    <testcase classname="Basic E2E Tests GraphQL Schema Validation should have required types in schema" name="Basic E2E Tests GraphQL Schema Validation should have required types in schema" time="0.035">
    </testcase>
  </testsuite>
  <testsuite name="Business Flow E2E Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-09T00:32:29" time="2.112" tests="8">
    <testcase classname="Business Flow E2E Tests GraphQL Business Operations should query customer by phone via GraphQL" name="Business Flow E2E Tests GraphQL Business Operations should query customer by phone via GraphQL" time="0.129">
    </testcase>
    <testcase classname="Business Flow E2E Tests GraphQL Business Operations should query nearby restaurants via GraphQL" name="Business Flow E2E Tests GraphQL Business Operations should query nearby restaurants via GraphQL" time="0.063">
    </testcase>
    <testcase classname="Business Flow E2E Tests GraphQL Business Operations should test order placement mutation schema" name="Business Flow E2E Tests GraphQL Business Operations should test order placement mutation schema" time="0.065">
    </testcase>
    <testcase classname="Business Flow E2E Tests API Endpoint Integration should handle WhatsApp webhook simulation" name="Business Flow E2E Tests API Endpoint Integration should handle WhatsApp webhook simulation" time="0.036">
    </testcase>
    <testcase classname="Business Flow E2E Tests API Endpoint Integration should validate application configuration" name="Business Flow E2E Tests API Endpoint Integration should validate application configuration" time="0.029">
    </testcase>
    <testcase classname="Business Flow E2E Tests Error Handling and Edge Cases should handle malformed GraphQL queries gracefully" name="Business Flow E2E Tests Error Handling and Edge Cases should handle malformed GraphQL queries gracefully" time="0.032">
    </testcase>
    <testcase classname="Business Flow E2E Tests Error Handling and Edge Cases should handle missing authentication gracefully" name="Business Flow E2E Tests Error Handling and Edge Cases should handle missing authentication gracefully" time="0.032">
    </testcase>
    <testcase classname="Business Flow E2E Tests Error Handling and Edge Cases should handle large payload gracefully" name="Business Flow E2E Tests Error Handling and Edge Cases should handle large payload gracefully" time="0.047">
    </testcase>
  </testsuite>
</testsuites>