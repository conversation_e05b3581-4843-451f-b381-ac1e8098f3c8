/**
 * Stress Testing Suite
 * 测试系统在极限负载下的表现和恢复能力
 */

const autocannon = require('autocannon');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { generateAuthToken } = require('../helpers/authHelper');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');

describe('Stress Testing', () => {
  let testApp;
  let testCustomer;
  let testRestaurant;
  let authToken;
  let baseURL;

  beforeAll(async () => {
    await connectTestDB({ useRealDatabase: true });

    testApp = await createTestApp();
    const port = testApp.port;
    baseURL = `http://localhost:${port}`;

    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    authToken = generateAuthToken(testCustomer._id);
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  describe('Extreme Load Tests', () => {
    test('should survive 500 concurrent connections', async () => {
      const query = `
        query {
          configuration {
            _id
            currency
            currencySymbol
            deliveryRate
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 500,
        duration: 30,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      // 在极限负载下，允许一定的错误率
      const errorRate = (result.errors / result.requests.total) * 100;
      const timeoutRate = (result.timeouts / result.requests.total) * 100;

      expect(errorRate).toBeLessThan(10); // 错误率 < 10%
      expect(timeoutRate).toBeLessThan(5); // 超时率 < 5%
      expect(result.requests.total).toBeGreaterThan(1000); // 至少处理1000个请求

      console.log('Extreme Load Test Results:', {
        connections: 500,
        duration: '30s',
        totalRequests: result.requests.total,
        requestsPerSecond: result.requests.mean,
        latency: result.latency,
        errorRate: `${errorRate.toFixed(2)}%`,
        timeoutRate: `${timeoutRate.toFixed(2)}%`
      });
    });

    test('should handle burst traffic patterns', async () => {
      const burstResults = [];
      
      // 模拟3轮突发流量
      for (let burst = 1; burst <= 3; burst++) {
        console.log(`Running burst ${burst}/3...`);
        
        const result = await autocannon({
          url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
          connections: 200,
          duration: 10,
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });

        burstResults.push({
          burst,
          requests: result.requests.total,
          latency: result.latency.mean,
          errors: result.errors,
          timeouts: result.timeouts
        });

        // 突发之间的间隔
        await new Promise(resolve => setTimeout(resolve, 5000));
      }

      // 验证系统在多轮突发流量下的表现
      burstResults.forEach((result, index) => {
        expect(result.requests).toBeGreaterThan(100);
        expect(result.latency).toBeLessThan(2000); // 2秒内响应
        
        console.log(`Burst ${index + 1} Results:`, result);
      });

      // 验证性能没有显著下降
      const firstBurstLatency = burstResults[0].latency;
      const lastBurstLatency = burstResults[burstResults.length - 1].latency;
      const performanceDegradation = (lastBurstLatency - firstBurstLatency) / firstBurstLatency;
      
      expect(performanceDegradation).toBeLessThan(2); // 性能下降不超过200%
    });
  });

  describe('Resource Exhaustion Tests', () => {
    test('should handle memory pressure gracefully', async () => {
      const initialMemory = process.memoryUsage();
      
      // 创建大量数据以增加内存压力
      const largeDataRequests = Array(100).fill().map((_, index) => ({
        url: `${baseURL}/api/orders`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          restaurantId: testRestaurant._id.toString(),
          customerId: testCustomer._id.toString(),
          items: Array(50).fill().map((_, itemIndex) => ({
            foodId: `food_${index}_${itemIndex}`,
            quantity: 1,
            price: 10.00,
            description: 'A'.repeat(1000) // 大量文本数据
          })),
          deliveryAddress: 'A'.repeat(500), // 长地址
          paymentMethod: 'CARD'
        })
      }));

      const result = await autocannon({
        requests: largeDataRequests,
        connections: 50,
        duration: 20
      });

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreaseMB = memoryIncrease / 1024 / 1024;

      expect(result.errors).toBeLessThan(result.requests.total * 0.2); // 错误率 < 20%
      expect(memoryIncreaseMB).toBeLessThan(500); // 内存增长 < 500MB

      console.log('Memory Pressure Test:', {
        initialMemory: `${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        finalMemory: `${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        increase: `${memoryIncreaseMB.toFixed(2)} MB`,
        requests: result.requests.total,
        errors: result.errors
      });
    });

    test('should handle connection pool exhaustion', async () => {
      // 创建大量长时间运行的请求
      const longRunningRequests = Array(100).fill().map(() =>
        autocannon({
          url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
          connections: 10,
          duration: 5,
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        })
      );

      const results = await Promise.allSettled(longRunningRequests);
      
      const successfulTests = results.filter(result => result.status === 'fulfilled');
      const failedTests = results.filter(result => result.status === 'rejected');

      // 大部分测试应该成功
      expect(successfulTests.length).toBeGreaterThan(80);
      expect(failedTests.length).toBeLessThan(20);

      console.log('Connection Pool Test:', {
        totalTests: 100,
        successful: successfulTests.length,
        failed: failedTests.length,
        successRate: `${(successfulTests.length / 100 * 100).toFixed(2)}%`
      });
    });
  });

  describe('Recovery and Resilience Tests', () => {
    test('should recover from temporary overload', async () => {
      // 第一阶段：正常负载
      const normalLoad = await autocannon({
        url: `${baseURL}/api/payments/methods`,
        connections: 50,
        duration: 10,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 第二阶段：极限负载
      const extremeLoad = await autocannon({
        url: `${baseURL}/api/payments/methods`,
        connections: 1000,
        duration: 15,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 等待系统恢复
      await new Promise(resolve => setTimeout(resolve, 10000));

      // 第三阶段：恢复后的正常负载
      const recoveryLoad = await autocannon({
        url: `${baseURL}/api/payments/methods`,
        connections: 50,
        duration: 10,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 验证系统恢复能力
      const normalLatency = normalLoad.latency.mean;
      const recoveryLatency = recoveryLoad.latency.mean;
      const latencyIncrease = (recoveryLatency - normalLatency) / normalLatency;

      expect(latencyIncrease).toBeLessThan(0.5); // 恢复后延迟增长 < 50%
      expect(recoveryLoad.errors).toBeLessThan(normalLoad.errors * 2);

      console.log('Recovery Test Results:', {
        normal: {
          latency: `${normalLatency.toFixed(2)}ms`,
          requests: normalLoad.requests.total,
          errors: normalLoad.errors
        },
        extreme: {
          latency: `${extremeLoad.latency.mean.toFixed(2)}ms`,
          requests: extremeLoad.requests.total,
          errors: extremeLoad.errors
        },
        recovery: {
          latency: `${recoveryLatency.toFixed(2)}ms`,
          requests: recoveryLoad.requests.total,
          errors: recoveryLoad.errors,
          latencyIncrease: `${(latencyIncrease * 100).toFixed(2)}%`
        }
      });
    });
  });

  describe('Database Stress Tests', () => {
    test('should handle database connection stress', async () => {
      const Order = require('../../models/order');
      const Customer = require('../../models/customer');
      
      // 创建大量并发数据库操作
      const dbOperations = [];
      
      for (let i = 0; i < 200; i++) {
        dbOperations.push(
          Customer.create({
            name: `Stress Test User ${i}`,
            email: `stress-${i}@example.com`,
            phone: `+1234567${i.toString().padStart(3, '0')}`
          })
        );
      }

      for (let i = 0; i < 100; i++) {
        dbOperations.push(
          Order.create({
            orderId: `STRESS-${i}`,
            restaurant: testRestaurant._id,
            user: testCustomer._id,
            orderAmount: 25.99,
            orderStatus: 'PENDING',
            paymentStatus: 'PENDING',
            orderDate: new Date(),
            items: [{
              _id: `item_${i}`,
              title: `Stress Test Item ${i}`,
              quantity: 1,
              price: 25.99
            }]
          })
        );
      }

      const startTime = Date.now();
      const results = await Promise.allSettled(dbOperations);
      const endTime = Date.now();

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const duration = endTime - startTime;

      expect(successful).toBeGreaterThan(250); // 至少83%成功
      expect(duration).toBeLessThan(30000); // 30秒内完成

      console.log('Database Stress Test:', {
        totalOperations: dbOperations.length,
        successful,
        failed,
        duration: `${duration}ms`,
        operationsPerSecond: ((successful / duration) * 1000).toFixed(2)
      });
    });
  });

  describe('API Rate Limiting Tests', () => {
    test('should enforce rate limits under stress', async () => {
      // 快速发送大量请求测试限流
      const rapidRequests = await autocannon({
        url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
        connections: 100,
        duration: 5,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 检查是否有429状态码（Too Many Requests）
      const rateLimitResponses = rapidRequests['2xx'] + rapidRequests['4xx'] + rapidRequests['5xx'];
      
      expect(rapidRequests.requests.total).toBeGreaterThan(100);
      
      // 如果实现了限流，应该看到一些429响应
      // 如果没有实现限流，至少系统应该保持稳定
      expect(rapidRequests.errors).toBeLessThan(rapidRequests.requests.total * 0.1);

      console.log('Rate Limiting Test:', {
        totalRequests: rapidRequests.requests.total,
        responses: {
          '2xx': rapidRequests['2xx'],
          '4xx': rapidRequests['4xx'],
          '5xx': rapidRequests['5xx']
        },
        errors: rapidRequests.errors,
        timeouts: rapidRequests.timeouts
      });
    });
  });
});
