<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Performance Tests" tests="9" failures="4" errors="0" time="113.089">
  <testsuite name="Load Testing" errors="0" failures="4" skipped="0" timestamp="2025-06-09T09:41:59" time="112.93" tests="9">
    <testcase classname="Load Testing API Endpoint Load Tests should handle 100 concurrent GraphQL requests" name="Load Testing API Endpoint Load Tests should handle 100 concurrent GraphQL requests" time="10.933">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 0
Received: 1400
    at toBe (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:75:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Load Testing API Endpoint Load Tests should handle GraphQL mutations under load" name="Load Testing API Endpoint Load Tests should handle GraphQL mutations under load" time="10.911">
    </testcase>
    <testcase classname="Load Testing API Endpoint Load Tests should handle payment methods query under load" name="Load Testing API Endpoint Load Tests should handle payment methods query under load" time="11.29">
    </testcase>
    <testcase classname="Load Testing GraphQL Load Tests should handle GraphQL queries under load" name="Load Testing GraphQL Load Tests should handle GraphQL queries under load" time="10.638">
      <failure>Error: expect(received).toBeLessThan(expected)

Expected: &lt; 300
Received:   726.2
    at toBeLessThan (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:206:35)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Load Testing GraphQL Load Tests should handle complex GraphQL queries under load" name="Load Testing GraphQL Load Tests should handle complex GraphQL queries under load" time="11.035">
    </testcase>
    <testcase classname="Load Testing Database Load Tests should handle concurrent database operations" name="Load Testing Database Load Tests should handle concurrent database operations" time="0.384">
      <failure>MongoServerError: E11000 duplicate key error collection: firespoon_test.customers index: phone_1 dup key: { phone: &quot;+1234567890&quot; }
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/operations/insert.ts:85:25
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection_pool.ts:569:13
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/sdam/server.ts:363:13
    at handleOperationResult (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/sdam/server.ts:496:14)
    at Connection.onMessage (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection.ts:461:5)
    at MessageStream.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/connection.ts:241:56)
    at MessageStream.emit (node:events:517:28)
    at processIncomingData (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/message_stream.ts:188:12)
    at MessageStream._write (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongodb/src/cmap/message_stream.ts:69:5)
    at writeOrBuffer (node:internal/streams/writable:392:12)
    at _write (node:internal/streams/writable:333:10)
    at MessageStream.Writable.write (node:internal/streams/writable:337:10)
    at Socket.ondata (node:internal/streams/readable:809:22)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)</failure>
    </testcase>
    <testcase classname="Load Testing Memory and Resource Tests should not have memory leaks under sustained load" name="Load Testing Memory and Resource Tests should not have memory leaks under sustained load" time="31.435">
    </testcase>
    <testcase classname="Load Testing Error Rate Tests should maintain low error rate under high load" name="Load Testing Error Rate Tests should maintain low error rate under high load" time="16.443">
    </testcase>
    <testcase classname="Performance Benchmarks should meet performance SLA requirements" name="Performance Benchmarks should meet performance SLA requirements" time="0.002">
      <failure>ReferenceError: testCustomer is not defined
    at testCustomer (/home/<USER>/firespoon/Firespoon_API_TF/test/performance/load.test.js:394:34)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
</testsuites>