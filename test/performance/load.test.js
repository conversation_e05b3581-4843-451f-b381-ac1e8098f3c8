/**
 * Load Testing Suite
 * 测试系统在正常负载下的性能表现
 */

const autocannon = require('autocannon');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { generateAuthToken } = require('../helpers/authHelper');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');

describe('Load Testing', () => {
  let testApp;
  let testCustomer;
  let testRestaurant;
  let authToken;
  let baseURL;

  beforeAll(async () => {
    // 连接测试数据库
    await connectTestDB({ useRealDatabase: true });

    // 启动测试服务器
    testApp = await createTestApp();
    const port = testApp.port;
    baseURL = `http://localhost:${port}`;

    // 创建测试数据
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    authToken = generateAuthToken(testCustomer._id);
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('API Endpoint Load Tests', () => {
    test('should handle 100 concurrent GraphQL requests', async () => {
      const query = `
        query GetOrdersByCustomer($customerId: String!) {
          ordersByCustomer(customerId: $customerId) {
            _id
            orderId
            orderStatus
            orderAmount
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 100,
        duration: 10, // 10 seconds
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query,
          variables: { customerId: testCustomer.customerId }
        })
      });

      // 验证性能指标
      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBe(0);
      expect(result.latency.mean).toBeLessThan(500); // 平均响应时间 < 500ms
      expect(result.requests.mean).toBeGreaterThan(50); // 每秒至少50个请求
      
      console.log('Load Test Results:', {
        requests: result.requests,
        latency: result.latency,
        throughput: result.throughput,
        errors: result.errors
      });
    });

    test('should handle GraphQL mutations under load', async () => {
      const mutation = `
        mutation PlaceOrder($restaurantId: ID!, $customerId: String!, $orderInput: [OrderInput!]!, $paymentMethod: String!, $orderDate: String!, $isPickedUp: Boolean!, $taxationAmount: Float!, $deliveryCharges: Float!) {
          placeOrder(
            restaurantId: $restaurantId
            customerId: $customerId
            orderInput: $orderInput
            paymentMethod: $paymentMethod
            orderDate: $orderDate
            isPickedUp: $isPickedUp
            taxationAmount: $taxationAmount
            deliveryCharges: $deliveryCharges
          ) {
            _id
            orderId
            orderStatus
          }
        }
      `;

      const variables = {
        restaurantId: testRestaurant._id.toString(),
        customerId: testCustomer.customerId,
        orderInput: [
          {
            food: 'food_1',
            variation: 'var_1',
            quantity: 1
          }
        ],
        paymentMethod: 'COD',
        orderDate: new Date().toISOString(),
        isPickedUp: false,
        taxationAmount: 1.04,
        deliveryCharges: 3.00
      };

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query: mutation, variables }),
        connections: 50,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(1000); // 创建订单 < 1s
      expect(result.requests.mean).toBeGreaterThan(20); // 每秒至少20个订单创建
    });

    test('should handle payment methods query under load', async () => {
      const query = `
        query {
          configuration {
            _id
            currency
            currencySymbol
            deliveryRate
            paypalSettings {
              clientId
              sandbox
            }
            stripeSettings {
              publishableKey
              sandbox
            }
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query }),
        connections: 30,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(2000); // 配置查询 < 2s
    });
  });

  describe('GraphQL Load Tests', () => {
    test('should handle GraphQL queries under load', async () => {
      const query = `
        query {
          restaurants {
            _id
            name
            isActive
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query }),
        connections: 100,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(300); // GraphQL查询 < 300ms
    });

    test('should handle complex GraphQL queries under load', async () => {
      const query = `
        query GetOrdersWithDetails($restaurantId: ID!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderId
            orderAmount
            orderStatus
            items {
              _id
              title
              quantity
            }
            user {
              _id
              name
              email
            }
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query,
          variables: { restaurantId: testRestaurant._id.toString() }
        }),
        connections: 50,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(800); // 复杂查询 < 800ms
    });
  });

  describe('Database Load Tests', () => {
    test('should handle concurrent database operations', async () => {
      const promises = [];
      const concurrentOperations = 100;

      // 创建100个并发的数据库操作
      for (let i = 0; i < concurrentOperations; i++) {
        promises.push(
          createCustomer({
            email: `load-test-${i}@example.com`,
            name: `Load Test User ${i}`
          })
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const endTime = Date.now();

      const duration = endTime - startTime;
      const operationsPerSecond = (concurrentOperations / duration) * 1000;

      expect(results.length).toBe(concurrentOperations);
      expect(duration).toBeLessThan(5000); // 5秒内完成
      expect(operationsPerSecond).toBeGreaterThan(20); // 每秒至少20个操作

      console.log(`Database Load Test: ${concurrentOperations} operations in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);
    });
  });

  describe('Memory and Resource Tests', () => {
    test('should not have memory leaks under sustained load', async () => {
      const initialMemory = process.memoryUsage();

      const query = `
        query {
          undeliveredOrders(offset: 0) {
            _id
            orderId
            orderStatus
            orderAmount
          }
        }
      `;

      // 运行持续负载测试
      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 50,
        duration: 30, // 30秒持续负载
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

      expect(result.errors).toBe(0);
      expect(memoryIncreasePercent).toBeLessThan(50); // 内存增长不超过50%

      console.log('Memory Usage:', {
        initial: `${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        final: `${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        increase: `${(memoryIncrease / 1024 / 1024).toFixed(2)} MB (${memoryIncreasePercent.toFixed(2)}%)`
      });
    });
  });

  describe('Error Rate Tests', () => {
    test('should maintain low error rate under high load', async () => {
      const query = `
        query {
          deliveredOrders(offset: 0) {
            _id
            orderId
            orderStatus
            orderAmount
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 200, // 高并发
        duration: 15,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      const errorRate = (result.errors / result.requests.total) * 100;
      const timeoutRate = (result.timeouts / result.requests.total) * 100;

      expect(errorRate).toBeLessThan(1); // 错误率 < 1%
      expect(timeoutRate).toBeLessThan(0.5); // 超时率 < 0.5%

      console.log('Error Rate Analysis:', {
        totalRequests: result.requests.total,
        errors: result.errors,
        timeouts: result.timeouts,
        errorRate: `${errorRate.toFixed(2)}%`,
        timeoutRate: `${timeoutRate.toFixed(2)}%`
      });
    });
  });
});

// 性能基准测试
describe('Performance Benchmarks', () => {
  test('should meet performance SLA requirements', async () => {
    const graphqlQueries = [
      {
        name: 'Order List',
        query: `
          query {
            allOrders(page: 0) {
              _id
              orderId
              orderStatus
              orderAmount
            }
          }
        `,
        variables: {},
        maxLatency: 500,
        minThroughput: 100
      },
      {
        name: 'Restaurant List',
        query: `
          query {
            restaurants {
              _id
              name
              isActive
              address
            }
          }
        `,
        variables: {},
        maxLatency: 200,
        minThroughput: 200
      }
    ];

    for (const endpoint of graphqlQueries) {
      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 50,
        duration: 10,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: endpoint.query,
          variables: endpoint.variables
        })
      });

      expect(result.latency.mean).toBeLessThan(endpoint.maxLatency);
      expect(result.requests.mean).toBeGreaterThan(endpoint.minThroughput);

      console.log(`${endpoint.name} Performance:`, {
        latency: `${result.latency.mean}ms (max: ${endpoint.maxLatency}ms)`,
        throughput: `${result.requests.mean} req/s (min: ${endpoint.minThroughput} req/s)`,
        status: result.latency.mean < endpoint.maxLatency && result.requests.mean > endpoint.minThroughput ? 'PASS' : 'FAIL'
      });
    }
  });
});
